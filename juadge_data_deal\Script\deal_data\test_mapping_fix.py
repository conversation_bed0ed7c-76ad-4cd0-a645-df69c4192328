# 测试映射修复的脚本
import pandas as pd
import sys
import os

# 添加脚本路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
import importlib.util
script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "xlsxgroup_to_sheet-v3.py")
spec = importlib.util.spec_from_file_location("xlsxgroup_to_sheet_v3", script_path)
xlsxgroup_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xlsxgroup_module)

# 从模块中导入函数
get_corrected_column_mapping = xlsxgroup_module.get_corrected_column_mapping
validate_column_mapping = xlsxgroup_module.validate_column_mapping
print_validation_results = xlsxgroup_module.print_validation_results
improved_column_mapping = xlsxgroup_module.improved_column_mapping
process_data_for_import = xlsxgroup_module.process_data_for_import

def test_column_mapping():
    """
    测试列映射功能
    """
    print("=== 测试列映射修复 ===")

    # 创建测试数据
    test_data = pd.DataFrame({
        '广告名称': ['广告1', '广告2', '广告3'],
        '点击量（全部）': [100, 200, 150],
        '网站完成注册次数': [5, 10, 8],
        '已花费金额 (USD)': [25.50, 45.30, 35.20],
        '帐户名称': ['账户001', '账户002', '账户003'],
        '报告结束日期': ['2025-01-01', '2025-01-02', '2025-01-03'],
        '展示次数': [1000, 2000, 1500]
    })

    print("测试数据:")
    print(test_data.head())
    print(f"列名: {test_data.columns.tolist()}")

    # 测试修正后的列映射配置
    print("\n1. 测试修正后的列映射配置:")
    corrected_mapping = get_corrected_column_mapping()
    for target, sources in corrected_mapping.items():
        print(f"  {target}: {sources}")

    # 测试改进的列映射函数
    print("\n2. 测试改进的列映射函数:")
    column_mappings = improved_column_mapping(test_data, "test_file.xlsx")

    # 测试数据处理函数
    print("\n3. 测试数据处理函数:")
    processed_data = process_data_for_import(test_data, source_file="test_file.xlsx")

    if processed_data:
        print(f"处理结果: {len(processed_data)} 行数据")
        print("第一行数据:")
        for key, value in processed_data[0].items():
            print(f"  {key}: {value}")
    else:
        print("处理失败")

    return processed_data

def test_real_data_structure():
    """
    测试实际Excel文件的数据结构
    """
    print("\n=== 测试实际数据结构 ===")

    # 模拟实际Excel文件的数据结构
    real_data = pd.DataFrame({
        'Unnamed: 0': ['AS01/250730', 'AS01/250730', 'AS01/250730'],
        'Unnamed: 1': ['AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730'],
        'Unnamed: 2': ['AS01/AG01/AQ/AD01/PH/TV/250730/1%MZ/343', 'AS01/AG01/AQ/AD02/PH/TV/250730/1%MZ/344', 'AS01/AG01/AQ/AD03/PH/TV/250730/1%MZ/345'],
        'Unnamed: 3': ['Mioeye--05', 'Mioeye--04', 'Mioeye--04'],
        1968: [273, 280, 260],  # 展示次数
        263: [35, 40, 30],      # 点击量
        7: [2, 3, 1],           # 成效
        'USD': ['USD', 'USD', 'USD'],
        3.27: [0.44, 0.50, 0.38],  # 可能是费用相关
        0.46714286: [0.435, 0.440, 0.430],
        13.36382114: [13.1, 13.5, 12.8],
        'Unnamed: 11': ['active', 'active', 'active'],
        '2025-07-31': ['2025-07-31', '2025-07-31', '2025-07-31'],
        '2025-07-31.1': ['2025-07-31', '2025-07-31', '2025-07-31']
    })

    print("实际数据结构:")
    print(real_data.head())
    print(f"列名: {real_data.columns.tolist()}")

    # 测试改进的列映射函数
    print("\n测试实际数据的列映射:")
    column_mappings = improved_column_mapping(real_data, "daily_analysis.xlsx")

    # 测试数据处理函数
    print("\n测试实际数据的处理:")
    processed_data = process_data_for_import(real_data, source_file="daily_analysis.xlsx")

    if processed_data:
        print(f"处理结果: {len(processed_data)} 行数据")
        print("第一行数据:")
        for key, value in processed_data[0].items():
            print(f"  {key}: {value}")
    else:
        print("处理失败")

    return processed_data

def test_edge_cases():
    """
    测试边缘情况
    """
    print("\n=== 测试边缘情况 ===")
    
    # 测试缺少列的情况
    incomplete_data = pd.DataFrame({
        '广告名称': ['广告1', '广告2'],
        '点击量（全部）': [100, 200],
        # 缺少其他必要列
    })
    
    print("1. 测试缺少列的情况:")
    print(f"不完整数据列名: {incomplete_data.columns.tolist()}")
    
    mappings = improved_column_mapping(incomplete_data, "incomplete_test.xlsx")
    print(f"映射结果: {mappings}")
    
    # 测试列名不匹配的情况
    mismatched_data = pd.DataFrame({
        'Ad Name': ['广告1', '广告2'],
        'Clicks': [100, 200],
        'Registrations': [5, 10],
        'Cost': [25.50, 45.30]
    })
    
    print("\n2. 测试列名不匹配的情况:")
    print(f"不匹配数据列名: {mismatched_data.columns.tolist()}")
    
    mappings = improved_column_mapping(mismatched_data, "mismatched_test.xlsx")
    print(f"映射结果: {mappings}")

if __name__ == "__main__":
    try:
        # 运行测试
        processed_data = test_column_mapping()
        test_edge_cases()
        
        print("\n=== 测试完成 ===")
        if processed_data:
            print("✅ 映射修复测试通过")
        else:
            print("❌ 映射修复测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
