# 测试重写的映射逻辑
import pandas as pd
import sys
import os
import numpy as np

# 添加脚本路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
import importlib.util
script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "xlsxgroup_to_sheet-v3.py")
spec = importlib.util.spec_from_file_location("xlsxgroup_to_sheet_v3", script_path)
xlsxgroup_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xlsxgroup_module)

# 从模块中导入函数
get_corrected_column_mapping = xlsxgroup_module.get_corrected_column_mapping
analyze_numeric_columns = xlsxgroup_module.analyze_numeric_columns
smart_numeric_mapping = xlsxgroup_module.smart_numeric_mapping
improved_column_mapping = xlsxgroup_module.improved_column_mapping
process_data_for_import = xlsxgroup_module.process_data_for_import

def test_real_excel_structure():
    """
    测试实际Excel文件的数据结构 - 重写版
    """
    print("=== 测试实际Excel数据结构的映射 ===")
    
    # 模拟实际Excel文件的数据结构
    real_data = pd.DataFrame({
        'Unnamed: 0': ['AS01/250730', 'AS01/250730', 'AS01/250730', 'AS02/250730', 'AS03/250730'],
        'Unnamed: 1': ['AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730', 'AS02/AG02/AQ/250730', 'AS03/AG03/AQ/250730'],
        'Unnamed: 2': [
            'AS01/AG01/AQ/AD01/PH/TV/250730/1%MZ/343', 
            'AS01/AG01/AQ/AD02/PH/TV/250730/1%MZ/344', 
            'AS01/AG01/AQ/AD03/PH/TV/250730/1%MZ/345',
            'AS02/AG02/AQ/AD04/PH/TV/250730/1%MZ/346',
            'AS03/AG03/AQ/AD05/PH/TV/250730/1%MZ/347'
        ],
        'Unnamed: 3': ['Mioeye--05', 'Mioeye--04', 'Mioeye--04', 'Mioeye--03', 'Mioeye--01'],
        1968: [273, 280, 260, 290, 250],  # 展示次数 - 最大数值
        263: [35, 40, 30, 45, 25],        # 点击量 - 第二大数值
        7: [2, 3, 1, 4, 2],               # 成效 - 小数值
        'USD': ['USD', 'USD', 'USD', 'USD', 'USD'],
        3.27: [0.44, 0.50, 0.38, 0.55, 0.42],  # 可能是费率
        0.46714286: [0.435, 0.440, 0.430, 0.445, 0.425],  # 可能是转化率
        13.36382114: [13.1, 13.5, 12.8, 14.2, 12.5],  # 已花费金额 - 有小数的中等数值
        'Unnamed: 11': ['active', 'active', 'active', 'active', 'active'],
        '2025-07-31': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31'],
        '2025-07-31.1': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31']
    })
    
    print("实际Excel数据结构:")
    print(real_data.head())
    print(f"列名: {real_data.columns.tolist()}")
    
    # 测试数值列分析
    print("\n=== 数值列详细分析 ===")
    numeric_cols = analyze_numeric_columns(real_data)
    
    print("\n数值列排序结果:")
    for i, col_info in enumerate(numeric_cols):
        print(f"{i+1}. {col_info['column']}: 平均={col_info['mean']:.2f}, 范围={col_info['range']:.2f}, 有小数={col_info['has_decimals']}")
    
    # 测试智能映射
    print("\n=== 智能映射测试 ===")
    test_columns = ['展示次数', '点击量（全部）', '成效', '已花费金额 (USD)']
    
    for col in test_columns:
        result = smart_numeric_mapping(col, numeric_cols)
        print(f"'{col}' -> {result}")
    
    # 测试完整的列映射
    print("\n=== 完整列映射测试 ===")
    column_mappings = improved_column_mapping(real_data, "daily_analysis.xlsx")
    
    print(f"\n最终映射结果:")
    for target, source in column_mappings.items():
        print(f"  {target} -> {source}")
    
    # 测试数据处理
    print("\n=== 数据处理测试 ===")
    processed_data = process_data_for_import(real_data, source_file="daily_analysis.xlsx")
    
    if processed_data:
        print(f"处理结果: {len(processed_data)} 行数据")
        print("第一行数据:")
        for key, value in processed_data[0].items():
            print(f"  {key}: {value}")
        
        # 验证关键字段
        first_row = processed_data[0]
        print(f"\n关键字段验证:")
        print(f"  展示次数: {first_row.get('展示次数')} (应该是较大数值)")
        print(f"  点击量（全部）: {first_row.get('点击量（全部）')} (应该是中等数值)")
        print(f"  成效: {first_row.get('成效')} (应该是小数值)")
        print(f"  已花费金额 (USD): {first_row.get('已花费金额 (USD)')} (应该有小数)")
        
        return True
    else:
        print("处理失败")
        return False

def test_edge_cases():
    """
    测试边缘情况
    """
    print("\n=== 测试边缘情况 ===")
    
    # 测试没有明显数值特征的数据
    edge_data = pd.DataFrame({
        'col1': [1, 2, 3],
        'col2': [4, 5, 6],
        'col3': [7, 8, 9],
        'col4': [10, 11, 12]
    })
    
    print("边缘情况数据:")
    print(edge_data)
    
    numeric_cols = analyze_numeric_columns(edge_data)
    print(f"\n数值列分析结果: {len(numeric_cols)} 个数值列")
    
    # 测试映射
    mappings = improved_column_mapping(edge_data, "edge_test.xlsx")
    print(f"映射结果: {mappings}")

if __name__ == "__main__":
    try:
        print("开始测试重写的映射逻辑...")
        
        # 测试实际Excel结构
        success = test_real_excel_structure()
        
        # 测试边缘情况
        test_edge_cases()
        
        print("\n=== 测试完成 ===")
        if success:
            print("✅ 重写的映射逻辑测试通过")
        else:
            print("❌ 重写的映射逻辑测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
